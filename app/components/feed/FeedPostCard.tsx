import { useState, useCallback, memo, useEffect } from "react";
import { type EnrichedActivity, type EnrichedReaction } from "getstream";
import {
  MessageSquare,
  Heart,
  Share2,
  MoreHorizontal,
  Send,
  Link,
  ZoomIn,
  Edit2,
} from "lucide-react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import { useFeedComments } from "~/lib/api/client-queries";
import { canUserEditPost } from "~/lib/utils/feed";
import { EditPostModal } from "./EditPostModal";

// Initialize dayjs plugins at module level (performance optimization)
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);

export interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  og?: {
    url?: string;
    title?: string;
    description?: string;
    images?: Array<{
      image?: string;
      url?: string;
    }>;
    site_name?: string;
    type?: string;
  };
  own_reactions?: {
    like?: EnrichedReaction[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

export interface FeedPostCardProps {
  activity: EnrichedActivityWithText;
  onLike: (activityId: string, isLiked: boolean, reactionId?: string) => void;
  onComment: (activityId: string, text: string) => void;
  onShare: (activityId: string) => void;
  onImageClick: (imageUrl: string, alt: string) => void;
  onPostClick?: (activityId: string) => void;
  onEdit?: (
    activityId: string,
    text: string,
    attachment?: {
      type: string;
      url: string;
      fileName?: string;
    }
  ) => Promise<void>;
  currentUserId?: string | null;
  showComments?: boolean;
  isClickable?: boolean;
  className?: string;
}

export const FeedPostCard = memo(
  ({
    activity,
    onLike,
    onComment,
    onShare,
    onImageClick,
    onPostClick,
    onEdit,
    currentUserId,
    showComments = false,
    isClickable = false,
    className = "",
  }: FeedPostCardProps) => {
    const [showCommentsState, setShowCommentsState] = useState(showComments);
    const [commentText, setCommentText] = useState("");
    const [shareDropdownOpen, setShareDropdownOpen] = useState(false);
    const [moreDropdownOpen, setMoreDropdownOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);

    const isLiked = (activity.own_reactions?.like?.length || 0) > 0;
    const likeReactionId = activity.own_reactions?.like?.[0]?.id;
    const canEdit = canUserEditPost(currentUserId || null, activity);

    // Use the comment loading hook
    const {
      data: comments,
      isLoading: commentsLoading,
      refetch: loadComments,
    } = useFeedComments(activity.id);

    const handleLike = useCallback(() => {
      onLike(activity.id, isLiked, likeReactionId);
    }, [activity.id, isLiked, likeReactionId, onLike]);

    const handleComment = useCallback(() => {
      if (commentText.trim()) {
        onComment(activity.id, commentText);
        setCommentText("");
      }
    }, [activity.id, commentText, onComment]);

    const handleShare = useCallback(() => {
      onShare(activity.id);
      setShareDropdownOpen(false);
    }, [activity.id, onShare]);

    const handleEditStart = useCallback(() => {
      setEditModalOpen(true);
      setMoreDropdownOpen(false);
    }, []);

    const handleEditModalClose = useCallback(() => {
      setEditModalOpen(false);
    }, []);

    const handleEditSave = useCallback(
      async (
        activityId: string,
        text: string,
        attachment?: {
          type: string;
          url: string;
          fileName?: string;
        }
      ) => {
        if (!onEdit) return;
        return onEdit(activityId, text, attachment);
      },
      [onEdit]
    );

    // Close dropdowns when clicking outside
    useEffect(() => {
      const handleClickOutside = () => {
        setShareDropdownOpen(false);
        setMoreDropdownOpen(false);
      };

      if (shareDropdownOpen || moreDropdownOpen) {
        document.addEventListener("click", handleClickOutside);
        return () => {
          document.removeEventListener("click", handleClickOutside);
        };
      }
    }, [shareDropdownOpen, moreDropdownOpen]);

    const formatTime = (timestamp: string) => {
      const date = dayjs.utc(timestamp).local();
      const now = dayjs();
      const diffInDays = now.diff(date, "day");

      if (diffInDays > 7) {
        return date.format("MMM D, YYYY");
      }

      return date.fromNow();
    };

    const getActorName = (actor: any): string => {
      if (typeof actor === "string") return actor;
      if (actor?.data?.name) return actor.data.name;
      if (actor?.id) return actor.id;
      return "Unknown User";
    };

    const getActorInitial = (actor: any): string => {
      const name = getActorName(actor);
      return name[0]?.toUpperCase() || "U";
    };

    const getActorImage = (actor: any): string | null => {
      if (actor?.data?.image) return actor.data.image;
      return null;
    };

    const getActivityContent = (activity: EnrichedActivityWithText): string => {
      if (activity.message) return activity.message;
      if (activity.text) return activity.text;
      if (typeof activity.object === "string") return activity.object;
      const obj = activity.object as any;
      if (obj?.text) return obj.text;
      if (obj?.content) return obj.content;
      if (obj?.id) return `${activity.verb} ${obj.id}`;
      return `${activity.verb}`;
    };

    // Function to convert URLs in text to clickable links
    const renderTextWithLinks = (text: string) => {
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const parts = text.split(urlRegex);

      return parts.map((part, index) => {
        if (part.match(urlRegex)) {
          return (
            <a
              key={index}
              href={part}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 underline"
              onClick={(e) => e.stopPropagation()}
            >
              {part}
            </a>
          );
        }
        return part;
      });
    };

    const cardClasses = `bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6 ${
      isClickable ? "cursor-pointer hover:bg-zinc-800/50 transition-colors" : ""
    } ${className}`;

    return (
      <div
        className={cardClasses}
        onClick={
          isClickable && onPostClick
            ? () => onPostClick(activity.id)
            : undefined
        }
      >
        {/* Activity Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {getActorImage(activity.actor) ? (
              <img
                src={getActorImage(activity.actor)!}
                alt={getActorName(activity.actor)}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">
                  {getActorInitial(activity.actor)}
                </span>
              </div>
            )}
            <div>
              <p className="font-medium text-white">
                {getActorName(activity.actor)}
              </p>
              <p className="text-sm text-zinc-400">
                {formatTime(activity.time)}
              </p>
            </div>
          </div>
          <div className="relative z-[100]">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setMoreDropdownOpen(!moreDropdownOpen);
              }}
              className="text-zinc-400 hover:text-white transition-colors"
            >
              <MoreHorizontal className="w-5 h-5" />
            </button>

            {moreDropdownOpen && (
              <div className="absolute top-full right-0 mt-2 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg py-1 min-w-[120px] z-[100]">
                {canEdit && onEdit && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditStart();
                      setMoreDropdownOpen(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm text-zinc-200 hover:bg-zinc-700 transition-colors flex items-center gap-2"
                  >
                    <Edit2 className="w-4 h-4" />
                    <span className="flex items-center gap-1">Edit Post</span>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Activity Content */}
        <div className="mb-4">
          <p className="text-zinc-200">
            {renderTextWithLinks(getActivityContent(activity))}
          </p>

          {/* Display legacy image field */}
          {activity.image && (
            <div className="mt-3 relative group cursor-pointer">
              <img
                src={activity.image}
                alt="Activity image"
                className="rounded-lg max-w-full transition-opacity group-hover:opacity-90"
                onClick={(e: React.MouseEvent) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onImageClick(activity.image!, "Activity image");
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg pointer-events-none">
                <ZoomIn className="w-8 h-8 text-white" />
              </div>
            </div>
          )}

          {/* Display attachments */}
          {activity.attachments && activity.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {activity.attachments.map((attachment, index) => (
                <div key={index}>
                  {attachment.type === "image" && attachment.image_url && (
                    <div className="relative group cursor-pointer">
                      <img
                        src={attachment.image_url}
                        alt="Attached image"
                        className="rounded-lg max-w-full transition-opacity group-hover:opacity-90"
                        onClick={(e: React.MouseEvent) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onImageClick(attachment.image_url!, "Attached image");
                        }}
                      />
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg pointer-events-none">
                        <ZoomIn className="w-8 h-8 text-white" />
                      </div>
                    </div>
                  )}
                  {attachment.type === "file" && attachment.asset_url && (
                    <a
                      href={attachment.asset_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-3 py-2 bg-zinc-100 rounded-lg hover:bg-zinc-200 transition-colors"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <span className="text-sm text-zinc-700">
                        📎 View File
                      </span>
                    </a>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Open Graph Preview */}
          {activity.og && (
            <div
              className="mt-3 border border-zinc-600 rounded-lg overflow-hidden bg-zinc-800 cursor-pointer hover:bg-zinc-700/50 transition-colors"
              onClick={(e: React.MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                if (activity.og?.url) {
                  window.open(activity.og.url, "_blank", "noopener,noreferrer");
                }
              }}
            >
              {activity.og.images &&
                activity.og.images.length > 0 &&
                activity.og.images[0].image && (
                  <img
                    src={activity.og.images[0].image}
                    alt={activity.og.title || "Link preview"}
                    className="w-full h-48 object-cover"
                  />
                )}
              <div className="p-4">
                {activity.og.title && (
                  <h4 className="font-semibold text-white text-sm mb-1 line-clamp-2">
                    {activity.og.title}
                  </h4>
                )}
                {activity.og.description && (
                  <p className="text-zinc-400 text-sm mb-2 line-clamp-2">
                    {activity.og.description}
                  </p>
                )}
                {activity.og.url && (
                  <p className="text-blue-400 text-xs truncate">
                    {new URL(activity.og.url).hostname}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Activity Actions */}
        <div className="flex items-center gap-6 pt-4 border-t border-zinc-700 relative z-10">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleLike();
            }}
            className={`flex items-center gap-2 transition-colors ${
              isLiked
                ? "text-blue-400 hover:text-blue-300"
                : "text-zinc-400 hover:text-blue-400"
            }`}
          >
            <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
            <span className="text-sm">
              {activity.reaction_counts?.like || 0}
            </span>
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              const newShowComments = !showCommentsState;
              setShowCommentsState(newShowComments);

              // Load comments if we're showing them and they haven't been loaded yet
              if (newShowComments && !comments) {
                loadComments();
              }
            }}
            className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
          >
            <MessageSquare className="w-5 h-5" />
            <span className="text-sm">
              {activity.reaction_counts?.comment || 0}
            </span>
          </button>

          <div className="relative z-[100]">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShareDropdownOpen(!shareDropdownOpen);
              }}
              className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
            >
              <Share2 className="w-5 h-5" />
              <span className="text-sm">Share</span>
            </button>

            {shareDropdownOpen && (
              <div className="absolute top-full left-0 mt-2 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg py-1 min-w-[140px] z-[100]">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleShare();
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-zinc-200 hover:bg-zinc-700 transition-colors flex items-center gap-2"
                >
                  <Link className="w-4 h-4" />
                  Copy Link
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Comments Section */}
        {showCommentsState && (
          <div
            className="mt-4 pt-4 border-t border-zinc-700 relative z-0"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Comment Input */}
            <div className="flex gap-2 mb-4">
              <input
                type="text"
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleComment();
                  }
                }}
                placeholder="Write a comment..."
                className="flex-1 px-3 py-2 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-zinc-400"
              />
              <button
                onClick={handleComment}
                disabled={!commentText.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>

            {/* Comments Loading State */}
            {commentsLoading && (
              <div className="flex justify-center py-4">
                <div className="w-5 h-5 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
              </div>
            )}

            {/* Show loaded comments */}
            {comments && comments.length > 0 && (
              <div className="space-y-3">
                {comments.map((comment: any) => (
                  <div key={comment.id} className="flex gap-3">
                    {comment.user?.data?.image ? (
                      <img
                        src={comment.user.data.image}
                        alt={comment.user.data.name || "User"}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                        <span className="text-zinc-300 text-sm font-medium">
                          {(comment.user?.data?.name || "U")[0].toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="bg-zinc-700 rounded-lg px-3 py-2">
                        <p className="text-sm font-medium text-white">
                          {comment.user?.data?.name || "Anonymous"}
                        </p>
                        <p className="text-sm text-zinc-300">
                          {comment.data?.text || String(comment.data)}
                        </p>
                      </div>
                      <p className="text-xs text-zinc-500 mt-1">
                        {formatTime(comment.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Show recent comments from the activity if no full comments loaded */}
            {!commentsLoading &&
              !comments &&
              (activity as any).latest_reactions?.comment && (
                <div className="space-y-3">
                  {(activity as any).latest_reactions.comment.map(
                    (comment: any) => (
                      <div key={comment.id} className="flex gap-3">
                        {comment.user?.data?.image ? (
                          <img
                            src={comment.user.data.image}
                            alt={comment.user.data.name || "User"}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                            <span className="text-zinc-300 text-sm font-medium">
                              {(comment.user?.data?.name ||
                                "U")[0].toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="bg-zinc-700 rounded-lg px-3 py-2">
                            <p className="text-sm font-medium text-white">
                              {comment.user?.data?.name || "Anonymous"}
                            </p>
                            <p className="text-sm text-zinc-300">
                              {comment.data?.text || String(comment.data)}
                            </p>
                          </div>
                          <p className="text-xs text-zinc-500 mt-1">
                            {formatTime(comment.created_at)}
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}

            {/* Empty state */}
            {!commentsLoading &&
              !comments &&
              !(activity as any).latest_reactions?.comment && (
                <p className="text-sm text-zinc-400 text-center py-2">
                  No comments yet. Be the first to comment!
                </p>
              )}
          </div>
        )}

        {/* Edit Post Modal */}
        {onEdit && (
          <EditPostModal
            activity={activity}
            isOpen={editModalOpen}
            onClose={handleEditModalClose}
            onSave={handleEditSave}
          />
        )}
      </div>
    );
  }
);

FeedPostCard.displayName = "FeedPostCard";
